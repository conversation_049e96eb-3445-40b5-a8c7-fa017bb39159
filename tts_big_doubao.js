const WebSocket = require('ws')
const zlib = require('zlib')
const { v4: uuidv4 } = require('uuid')
const fs = require('fs')

const MESSAGE_TYPES = {
  11: 'audio-only server response',
  12: 'frontend server response',
  15: 'error message from server',
}

const MESSAGE_TYPE_SPECIFIC_FLAGS = {
  0: 'no sequence number',
  1: 'sequence number > 0',
  2: 'last message from server (seq < 0)',
  3: 'sequence number < 0',
}

const MESSAGE_SERIALIZATION_METHODS = {
  0: 'no serialization',
  1: 'JSON',
  15: 'custom type',
}

const MESSAGE_COMPRESSIONS = {
  0: 'no compression',
  1: 'gzip',
  15: 'custom compression method',
}

const appid = '8359578722'
const token = 'Zol8wcj36NJGQC141tLZgNkvdVg-o_a7'
const cluster = 'volcano_icl'
const voice_type = 'zh_female_kailang<PERSON><PERSON><PERSON>_moon_bigtts'
const host = 'openspeech.bytedance.com'
const api_url = `wss://${host}/api/v1/tts/ws_binary`

// version: b0001 (4 bits)
// header size: b0001 (4 bits)
// message type: b0001 (Full client request) (4bits)
// message type specific flags: b0000 (none) (4bits)
// message serialization method: b0001 (JSON) (4 bits)
// message compression: b0001 (gzip) (4bits)
// reserved data: 0x00 (1 byte)
const default_header = Buffer.from([0x11, 0x10, 0x11, 0x00])

const request_json = {
  app: {
    appid: appid,
    token: 'access_token',
    cluster: cluster,
  },
  user: {
    uid: '388808087185088',
  },
  audio: {
    voice_type: 'xxx',
    encoding: 'mp3',
    speed_ratio: 1.0,
    volume_ratio: 1.0,
    pitch_ratio: 1.0,
  },
  request: {
    reqid: 'xxx',
    text: '你好，我是养生大师，今天有什么健康养生的问题想和我聊聊吗？',
    text_type: 'ssml',
    operation: 'xxx',
  },
}

function parseResponse(res, file) {
  const protocol_version = res[0] >> 4
  const header_size = res[0] & 0x0f
  const message_type = res[1] >> 4
  const message_type_specific_flags = res[1] & 0x0f
  const serialization_method = res[2] >> 4
  const message_compression = res[2] & 0x0f
  const reserved = res[3]
  const header_extensions = res.slice(4, header_size * 4)
  const payload = res.slice(header_size * 4)

  if (header_size !== 1) {
    console.log(`           Header extensions: ${header_extensions}`)
  }

  if (message_type === 0xb) {
    // audio-only server response
    if (message_type_specific_flags === 0) {
      // no sequence number as ACK
      console.log('                Payload size: 0')
      return false
    } else {
      const sequence_number = payload.readInt32BE(0)
      const payload_size = payload.readUInt32BE(4)
      const audio_payload = payload.slice(8)
      console.log('write audio payload to file')
      file.write(audio_payload)
      return sequence_number < 0
    }
  } else if (message_type === 0xf) {
    const code = payload.readUInt32BE(0)
    const msg_size = payload.readUInt32BE(4)
    let error_msg = payload.slice(8)
    if (message_compression === 1) {
      error_msg = zlib.gunzipSync(error_msg)
    }
    error_msg = error_msg.toString('utf-8')
    console.log(`          Error message code: ${code}`)
    console.log(`          Error message size: ${msg_size} bytes`)
    console.log(`               Error message: ${error_msg}`)
    return true
  } else if (message_type === 0xc) {
    const msg_size = payload.readUInt32BE(0)
    let frontend_payload = payload.slice(4)
    if (message_compression === 1) {
      frontend_payload = zlib.gunzipSync(frontend_payload)
    }
    console.log(`            Frontend message: ${frontend_payload}`)
  } else {
    console.log('undefined message type!')
    return true
  }
}

async function testSubmit() {
  const submit_request_json = JSON.parse(JSON.stringify(request_json))
  submit_request_json.audio.voice_type = voice_type
  submit_request_json.request.reqid = uuidv4()
  submit_request_json.request.operation = 'submit'

  let payload_bytes = Buffer.from(JSON.stringify(submit_request_json))
  payload_bytes = zlib.gzipSync(payload_bytes)

  const full_client_request = Buffer.concat([
    default_header,
    Buffer.alloc(4),
    payload_bytes,
  ])
  full_client_request.writeUInt32BE(payload_bytes.length, 4)

  console.log(
    "\n------------------------ test 'submit' -------------------------"
  )
  console.log('request json: ', submit_request_json)
  console.log('\nrequest bytes: ', full_client_request)

  const file = fs.createWriteStream('test_submit.mp3')
  const ws = new WebSocket(api_url, {
    headers: { Authorization: `Bearer; ${token}` },
  })

  return new Promise((resolve, reject) => {
    ws.on('open', () => {
      ws.send(full_client_request)
    })

    ws.on('message', (data) => {
      const done = parseResponse(data, file)
      if (done) {
        file.end()
        ws.close()
        resolve()
      }
    })

    ws.on('error', (error) => {
      reject(error)
    })
  })
}

async function testQuery({ voiceType = '', text, outputPath = 'output' }) {
  const query_request_json = JSON.parse(JSON.stringify(request_json))
  query_request_json.audio.voice_type = voiceType
  query_request_json.request.reqid = uuidv4()
  query_request_json.request.operation = 'query'
  query_request_json.request.text = text

  let payload_bytes = Buffer.from(JSON.stringify(query_request_json))
  payload_bytes = zlib.gzipSync(payload_bytes)

  const full_client_request = Buffer.concat([
    default_header,
    Buffer.alloc(4),
    payload_bytes,
  ])
  full_client_request.writeUInt32BE(payload_bytes.length, 4)


  const file = fs.createWriteStream(outputPath + '.mp3')
  const ws = new WebSocket(api_url, {
    headers: { Authorization: `Bearer; ${token}` },
  })

  return new Promise((resolve, reject) => {
    ws.on('open', () => {
      ws.send(full_client_request)
    })

    ws.on('message', (data) => {
      parseResponse(data, file)
      file.end()
      ws.close()
      resolve()
    })

    ws.on('error', (error) => {
      reject(error)
    })
  })
}

async function main() {
  try {
    // await testSubmit();
    console.log(new Date().toISOString(), 'start')
    await testQuery({
      voiceType: 'S_zXwnpk4s1',
      // voiceType: 'zh_male_yangguangqingnian_moon_bigtts',
      // text: `<speak>
      //     <phoneme alphabet="py" ph="zai4 ne5">在呢</phoneme>
      // </speak>`,
      text: `亲爱的，终于等到你了~ 今天有没有想我呀？`,
      outputPath: '哈哈哈',
    }).then(() => {
      console.log(new Date().toISOString(),'done')
    })
  } catch (error) {
    console.error('Error:', error)
  }
}

main()
